using Dalamud.Configuration;
using Dalamud.Plugin;
using System;
using System.Numerics;

namespace PvPLinePlugin;

public enum IndicatorType
{
    SolidLines,         // Basic solid lines
    DashedLines,        // Dashed/dotted lines
    GlowLines,          // Lines with glow effect
    AnimatedLines,      // Lines with animation effects
    ThickOutlines,      // Thick outlined lines
    GradientLines,      // Color gradient lines
    PulsingLines,       // Pulsing/breathing lines
    DirectionalArrows,  // Arrows pointing to targets
    HealthBars,         // Health bar indicators
    RoleIcons,          // Role-based icons
    ThreatIndicators,   // Threat level indicators
    Combination         // Multiple indicators combined
}

[Serializable]
public class Configuration : IPluginConfiguration
{
    public int Version { get; set; } = 0;
    public bool Enabled { get; set; } = true;

    #region Visual Indicator Settings
    private IndicatorType _indicatorType = IndicatorType.SolidLines;
    private Vector4 _lineColor = new Vector4(1.0f, 0.0f, 0.0f, 1.0f);
    private float _lineThickness = 2.0f;
    private float _outlineThickness = 3.0f;
    private float _iconSize = 20.0f;
    private float _arrowSize = 15.0f;
    private float _glowIntensity = 2.0f;
    private float _animationSpeed = 1.0f;
    private float _dashLength = 10.0f;
    private float _pulseSpeed = 2.0f;

    public IndicatorType IndicatorType
    {
        get => _indicatorType;
        set => _indicatorType = value;
    }

    public Vector4 LineColor
    {
        get => _lineColor;
        set => _lineColor = ValidateColor(value);
    }

    public float LineThickness
    {
        get => _lineThickness;
        set => _lineThickness = Math.Clamp(value, 1.0f, 15.0f);
    }

    public bool ColorCodeByRole { get; set; } = true;

    // Alternative indicator settings
    public float OutlineThickness
    {
        get => _outlineThickness;
        set => _outlineThickness = Math.Clamp(value, 1.0f, 15.0f);
    }

    public float IconSize
    {
        get => _iconSize;
        set => _iconSize = Math.Clamp(value, 5.0f, 100.0f);
    }

    public float ArrowSize
    {
        get => _arrowSize;
        set => _arrowSize = Math.Clamp(value, 5.0f, 50.0f);
    }

    public float GlowIntensity
    {
        get => _glowIntensity;
        set => _glowIntensity = Math.Clamp(value, 0.5f, 5.0f);
    }

    public float AnimationSpeed
    {
        get => _animationSpeed;
        set => _animationSpeed = Math.Clamp(value, 0.1f, 5.0f);
    }

    public float DashLength
    {
        get => _dashLength;
        set => _dashLength = Math.Clamp(value, 3.0f, 30.0f);
    }

    public float PulseSpeed
    {
        get => _pulseSpeed;
        set => _pulseSpeed = Math.Clamp(value, 0.5f, 5.0f);
    }

    public bool ShowDirectionalArrows { get; set; } = true;
    public bool PulseIndicators { get; set; } = false;
    #endregion

    #region Distance Settings
    private float _maxDistance = 50.0f;
    private float _playerListMaxDistance = 100.0f;

    public float MaxDistance
    {
        get => _maxDistance;
        set => _maxDistance = Math.Clamp(value, 5.0f, 200.0f);
    }

    public bool ShowDistance { get; set; } = true;
    #endregion

    #region PvP Settings
    public bool OnlyInPvP { get; set; } = true;
    public bool ShowInCombatOnly { get; set; } = false;
    #endregion

    #region Player Information
    public bool ShowPlayerNames { get; set; } = false;
    public bool ShowPlayerJobs { get; set; } = true;
    public bool ShowJobIcons { get; set; } = false;
    public bool ShowStatusEffects { get; set; } = true;
    public bool ShowOnlyImportantStatus { get; set; } = true;
    public bool ShowDefensiveBuffs { get; set; } = true;
    #endregion

    #region Low Health Settings
    public bool ShowLowHealthIndicator { get; set; } = true;
    public float LowHealthThreshold { get; set; } = 25.0f;
    public Vector4 LowHealthLineColor { get; set; } = new Vector4(1.0f, 0.8f, 0.0f, 1.0f);
    public float LowHealthLineThickness { get; set; } = 5.0f;
    public bool ShowHealthPercentage { get; set; } = true;
    public bool PulseKillableTargets { get; set; } = true;
    #endregion

    #region Enemy/Ally Detection
    public bool ShowAllies { get; set; } = false;
    public bool ShowEnemies { get; set; } = true;
    public Vector4 AllyLineColor { get; set; } = new Vector4(0.0f, 1.0f, 0.0f, 0.8f);
    public float AllyLineThickness { get; set; } = 2.0f;
    public bool ShowAllyEnemyIndicator { get; set; } = true;
    public bool DifferentColorsForAllies { get; set; } = true;
    #endregion

    #region Focus Target Settings
    public bool EnableFocusTarget { get; set; } = true;
    public string FocusTargetName { get; set; } = string.Empty;
    public Vector4 FocusTargetColor { get; set; } = new Vector4(1.0f, 1.0f, 0.0f, 1.0f); // Yellow
    public float FocusTargetThickness { get; set; } = 5.0f;
    public bool FocusTargetPulse { get; set; } = true;
    public bool FocusTargetAlwaysVisible { get; set; } = true; // Show even if outside normal distance
    public bool ShowFocusTargetInfo { get; set; } = true;
    public IndicatorType FocusTargetIndicatorType { get; set; } = IndicatorType.GlowLines;
    public bool EnableFocusTargetHotkey { get; set; } = true;
    #endregion

    #region Player List Settings
    public bool ShowPlayerList { get; set; } = false;
    public bool PlayerListShowAllies { get; set; } = true;
    public bool PlayerListShowEnemies { get; set; } = true;
    public bool PlayerListShowDistance { get; set; } = true;
    public bool PlayerListShowHealth { get; set; } = true;
    public bool PlayerListShowJob { get; set; } = true;
    public bool PlayerListShowStatus { get; set; } = true;
    public bool PlayerListAutoHide { get; set; } = false; // Hide when not in PvP
    public float PlayerListMaxDistance
    {
        get => _playerListMaxDistance;
        set => _playerListMaxDistance = Math.Clamp(value, 10.0f, 300.0f);
    }
    public bool PlayerListClickToFocus { get; set; } = true; // Click player name to set as focus target
    #endregion

    #region Targeting Detection Settings
    public bool EnableTargetingDetection { get; set; } = true;
    public bool ShowTargetingIndicators { get; set; } = true;
    public bool ShowTargetingWarnings { get; set; } = true;
    public bool ShowLineOfSight { get; set; } = true;
    public bool ShowCastingIndicators { get; set; } = true;
    public Vector4 TargetingLineColor { get; set; } = new Vector4(1.0f, 0.0f, 1.0f, 0.8f); // Magenta
    public Vector4 CriticalThreatColor { get; set; } = new Vector4(1.0f, 0.0f, 0.0f, 1.0f); // Red
    public Vector4 HighThreatColor { get; set; } = new Vector4(1.0f, 0.5f, 0.0f, 1.0f); // Orange
    public Vector4 ModerateThreatColor { get; set; } = new Vector4(1.0f, 1.0f, 0.0f, 1.0f); // Yellow
    public float TargetingLineThickness { get; set; } = 3.0f;
    public bool PulseTargetingIndicators { get; set; } = true;
    public bool ShowTargetingRecommendations { get; set; } = true;
    public bool PlayTargetingAlerts { get; set; } = false; // Audio alerts for future implementation

    // Advanced targeting detection settings
    public bool EnableAdvancedTargeting { get; set; } = true;
    public bool ShowMovementPrediction { get; set; } = true;
    public bool ShowThreatScores { get; set; } = true;
    public bool ShowSoftTargeting { get; set; } = true;
    public bool ShowProximityThreats { get; set; } = true;
    public float ThreatScoreThreshold { get; set; } = 3.0f;
    #endregion

    #region Quick Toggle Overlay Settings
    public bool EnableQuickToggleOverlay { get; set; } = true;
    public bool ShowQuickToggleOnHover { get; set; } = true;
    public Vector2 QuickTogglePosition { get; set; } = new Vector2(50, 50);
    public float QuickToggleOpacity { get; set; } = 0.8f;
    public bool QuickToggleCompactMode { get; set; } = false;
    #endregion

    public void Save() => Plugin.PluginInterface.SavePluginConfig(this);

    // Validation methods
    private static Vector4 ValidateColor(Vector4 color)
    {
        return new Vector4(
            Math.Clamp(color.X, 0.0f, 1.0f),
            Math.Clamp(color.Y, 0.0f, 1.0f),
            Math.Clamp(color.Z, 0.0f, 1.0f),
            Math.Clamp(color.W, 0.0f, 1.0f)
        );
    }
}
