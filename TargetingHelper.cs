using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using Dalamud.Game.ClientState.Objects.SubKinds;
using Dalamud.Game.ClientState.Objects.Types;

namespace PvPLinePlugin;

public enum TargetingThreatLevel
{
    None,           // No one targeting you
    Low,            // 1 enemy targeting you
    Moderate,       // 2 enemies targeting you
    High,           // 3+ enemies targeting you
    Critical,       // Multiple enemies + dangerous abilities incoming
    Extreme         // Overwhelming threat - immediate escape needed
}

public enum TargetingType
{
    Direct,         // Enemy has you as their target
    Soft,           // Enemy is facing you but targeting someone else
    Casting,        // Enemy is casting an ability that could hit you
    Proximity,      // Enemy is close enough to be a threat
    Predicted       // AI prediction based on enemy behavior
}

public enum EscapeRecommendation
{
    None,           // No action needed
    Reposition,     // Move to better position
    DefensiveCooldown, // Use defensive abilities
    Escape,         // Use escape abilities
    CallForHelp,    // Request ally assistance
    ImmediateRetreat // Drop everything and run
}

public struct AdvancedTargetingInfo
{
    public IPlayerCharacter Targeter { get; init; }
    public TargetingType TargetingType { get; init; }
    public float Distance { get; init; }
    public float ThreatScore { get; init; }
    public PlayerRole Role { get; init; }
    public bool IsCasting { get; init; }
    public string? CastingAbility { get; init; }
    public float CastTimeRemaining { get; init; }
    public bool HasLineOfSight { get; init; }
    public bool CanReachYou { get; init; }
    public Vector3 PredictedPosition { get; init; }
    public float MovementSpeed { get; init; }
    public ThreatLevel StatusThreatLevel { get; init; }
    public DateTime FirstDetected { get; init; }
    public DateTime LastSeen { get; init; }
    public int ConsecutiveTargetingFrames { get; init; }
    public bool IsNewThreat { get; init; }
    public EscapeRecommendation EscapeRecommendation { get; init; }
}

public struct TargetingInfo
{
    public IPlayerCharacter Targeter { get; init; }
    public float Distance { get; init; }
    public PlayerRole Role { get; init; }
    public bool IsCasting { get; init; }
    public bool HasLineOfSight { get; init; }
    public ThreatLevel ThreatLevel { get; init; }
}

public static class AdvancedTargetingDetector
{
    // Enhanced caching system
    private static readonly Dictionary<(ulong, ulong), (bool hasLOS, DateTime lastCheck)> _losCache = new();
    private static readonly Dictionary<ulong, TargetingHistory> _targetingHistory = new();
    private static readonly Dictionary<ulong, Vector3> _lastKnownPositions = new();
    private static readonly Dictionary<ulong, DateTime> _lastMovementUpdate = new();
    private static readonly TimeSpan _cacheExpiry = TimeSpan.FromMilliseconds(250); // Faster updates
    private static readonly TimeSpan _historyExpiry = TimeSpan.FromSeconds(10); // Keep history for 10 seconds

    // Threat calculation constants
    private const float MELEE_RANGE = 8.0f;
    private const float CAST_DANGER_RANGE = 30.0f;
    private const float PROXIMITY_THREAT_RANGE = 15.0f;
    private const float MOVEMENT_PREDICTION_TIME = 2.0f; // Predict 2 seconds ahead

    private struct TargetingHistory
    {
        public DateTime FirstTargeted { get; init; }
        public DateTime LastTargeted { get; init; }
        public int ConsecutiveFrames { get; init; }
        public bool WasCasting { get; init; }
        public Vector3 LastPosition { get; init; }
        public float AverageDistance { get; init; }
        public TargetingType LastTargetingType { get; init; }
    }

    /// <summary>
    /// Enhanced targeting detection with multiple detection methods
    /// </summary>
    public static bool IsTargetingMe(IPlayerCharacter enemy, IPlayerCharacter localPlayer)
    {
        if (enemy?.IsValid() != true || localPlayer?.IsValid() != true)
            return false;

        return enemy.TargetObjectId == localPlayer.GameObjectId;
    }

    /// <summary>
    /// Detect soft targeting (enemy facing you but not directly targeting)
    /// </summary>
    public static bool IsSoftTargetingMe(IPlayerCharacter enemy, IPlayerCharacter localPlayer)
    {
        if (enemy?.IsValid() != true || localPlayer?.IsValid() != true)
            return false;

        // Calculate if enemy is facing towards you
        var enemyToPlayer = Vector3.Normalize(localPlayer.Position - enemy.Position);
        var enemyFacing = GetFacingDirection(enemy);
        var facingDot = Vector3.Dot(enemyToPlayer, enemyFacing);

        // Consider it soft targeting if facing within 45 degrees and close enough
        return facingDot > 0.7f && Vector3.Distance(enemy.Position, localPlayer.Position) < PROXIMITY_THREAT_RANGE;
    }

    /// <summary>
    /// Detect casting threats (abilities that could hit you)
    /// </summary>
    public static bool IsCastingThreatToMe(IPlayerCharacter enemy, IPlayerCharacter localPlayer)
    {
        if (enemy?.IsValid() != true || localPlayer?.IsValid() != true || !enemy.IsCasting)
            return false;

        var distance = Vector3.Distance(enemy.Position, localPlayer.Position);

        // Different threat ranges based on role
        var threatRange = JobHelper.GetPlayerRole(enemy) switch
        {
            PlayerRole.MagicalRangedDPS => 30.0f,
            PlayerRole.PhysicalRangedDPS => 25.0f,
            PlayerRole.Healer => 25.0f,
            PlayerRole.MeleeDPS => 15.0f,
            PlayerRole.Tank => 20.0f,
            _ => 20.0f
        };

        return distance <= threatRange;
    }

    /// <summary>
    /// Get comprehensive targeting analysis for all enemies
    /// </summary>
    public static List<AdvancedTargetingInfo> GetAdvancedTargetingAnalysis(List<IPlayerCharacter> enemies, IPlayerCharacter localPlayer)
    {
        if (localPlayer?.IsValid() != true)
            return new List<AdvancedTargetingInfo>();

        var targetingAnalysis = new List<AdvancedTargetingInfo>();
        var currentTime = DateTime.UtcNow;

        // Clean up old history
        CleanupOldHistory(currentTime);

        foreach (var enemy in enemies)
        {
            if (enemy?.IsValid() != true) continue;

            var analysis = AnalyzeEnemyTargeting(enemy, localPlayer, currentTime);
            if (analysis.HasValue)
            {
                targetingAnalysis.Add(analysis.Value);
            }
        }

        // Sort by threat score (highest first)
        return targetingAnalysis.OrderByDescending(t => t.ThreatScore).ToList();
    }

    /// <summary>
    /// Analyze a single enemy's targeting behavior
    /// </summary>
    private static AdvancedTargetingInfo? AnalyzeEnemyTargeting(IPlayerCharacter enemy, IPlayerCharacter localPlayer, DateTime currentTime)
    {
        var enemyId = enemy.GameObjectId;
        var distance = Vector3.Distance(localPlayer.Position, enemy.Position);
        var role = JobHelper.GetPlayerRole(enemy);

        // Determine targeting type
        var targetingType = DetermineTargetingType(enemy, localPlayer);
        if (targetingType == TargetingType.Direct || targetingType == TargetingType.Casting ||
            (targetingType == TargetingType.Soft && distance < PROXIMITY_THREAT_RANGE))
        {
            // Update targeting history
            UpdateTargetingHistory(enemyId, currentTime, enemy.Position, targetingType);
            var history = _targetingHistory.GetValueOrDefault(enemyId);

            // Calculate comprehensive threat score
            var threatScore = CalculateAdvancedThreatScore(enemy, localPlayer, targetingType, history, distance);

            // Get movement prediction
            var predictedPosition = PredictEnemyPosition(enemy, MOVEMENT_PREDICTION_TIME);
            var movementSpeed = CalculateMovementSpeed(enemy);

            // Determine escape recommendation
            var escapeRecommendation = DetermineEscapeRecommendation(threatScore, targetingType, distance, role);

            return new AdvancedTargetingInfo
            {
                Targeter = enemy,
                TargetingType = targetingType,
                Distance = distance,
                ThreatScore = threatScore,
                Role = role,
                IsCasting = enemy.IsCasting,
                CastingAbility = GetCastingAbilityName(enemy),
                CastTimeRemaining = GetRemainingCastTime(enemy),
                HasLineOfSight = HasAdvancedLineOfSight(enemy, localPlayer),
                CanReachYou = CanEnemyReachYou(enemy, localPlayer, distance),
                PredictedPosition = predictedPosition,
                MovementSpeed = movementSpeed,
                StatusThreatLevel = StatusEffectHelper.GetThreatLevel(enemy),
                FirstDetected = history.FirstTargeted,
                LastSeen = currentTime,
                ConsecutiveTargetingFrames = history.ConsecutiveFrames,
                IsNewThreat = (currentTime - history.FirstTargeted).TotalSeconds < 1.0,
                EscapeRecommendation = escapeRecommendation
            };
        }

        return null;
    }

    /// <summary>
    /// Calculate advanced threat level based on comprehensive analysis
    /// </summary>
    public static TargetingThreatLevel GetAdvancedThreatLevel(List<AdvancedTargetingInfo> targetingAnalysis)
    {
        if (!targetingAnalysis.Any())
            return TargetingThreatLevel.None;

        var totalThreatScore = targetingAnalysis.Sum(t => t.ThreatScore);
        var directTargeters = targetingAnalysis.Count(t => t.TargetingType == TargetingType.Direct);
        var casters = targetingAnalysis.Count(t => t.IsCasting);
        var closeEnemies = targetingAnalysis.Count(t => t.Distance < MELEE_RANGE);
        var newThreats = targetingAnalysis.Count(t => t.IsNewThreat);

        // Extreme threat conditions
        if (totalThreatScore >= 15.0f || directTargeters >= 4 || (directTargeters >= 3 && casters >= 2))
            return TargetingThreatLevel.Extreme;

        // Critical threat conditions
        if (totalThreatScore >= 10.0f || directTargeters >= 3 || (directTargeters >= 2 && casters >= 2) ||
            (directTargeters >= 2 && closeEnemies >= 2))
            return TargetingThreatLevel.Critical;

        // High threat conditions
        if (totalThreatScore >= 6.0f || directTargeters >= 2 || (directTargeters >= 1 && casters >= 2) ||
            (directTargeters >= 1 && closeEnemies >= 2))
            return TargetingThreatLevel.High;

        // Moderate threat
        if (totalThreatScore >= 3.0f || directTargeters >= 1 || newThreats >= 2)
            return TargetingThreatLevel.Moderate;

        // Low threat
        return TargetingThreatLevel.Low;
    }

    #region Helper Methods

    private static TargetingType DetermineTargetingType(IPlayerCharacter enemy, IPlayerCharacter localPlayer)
    {
        if (IsTargetingMe(enemy, localPlayer))
            return TargetingType.Direct;

        if (IsCastingThreatToMe(enemy, localPlayer))
            return TargetingType.Casting;

        if (IsSoftTargetingMe(enemy, localPlayer))
            return TargetingType.Soft;

        var distance = Vector3.Distance(enemy.Position, localPlayer.Position);
        if (distance < PROXIMITY_THREAT_RANGE)
            return TargetingType.Proximity;

        return TargetingType.Predicted;
    }

    private static float CalculateAdvancedThreatScore(IPlayerCharacter enemy, IPlayerCharacter localPlayer,
        TargetingType targetingType, TargetingHistory history, float distance)
    {
        var baseScore = targetingType switch
        {
            TargetingType.Direct => 5.0f,
            TargetingType.Casting => 4.0f,
            TargetingType.Soft => 2.0f,
            TargetingType.Proximity => 1.5f,
            TargetingType.Predicted => 1.0f,
            _ => 0.0f
        };

        // Distance modifier (closer = more dangerous)
        var distanceModifier = Math.Max(0.1f, 1.0f - (distance / 30.0f));
        baseScore *= distanceModifier;

        // Role-based modifiers
        var roleModifier = JobHelper.GetPlayerRole(enemy) switch
        {
            PlayerRole.MeleeDPS => 1.3f,
            PlayerRole.MagicalRangedDPS => 1.2f,
            PlayerRole.PhysicalRangedDPS => 1.1f,
            PlayerRole.Tank => 0.9f,
            PlayerRole.Healer => 0.8f,
            _ => 1.0f
        };
        baseScore *= roleModifier;

        // Status effect modifiers
        var statusModifier = StatusEffectHelper.GetTargetingThreatMultiplier(enemy);
        baseScore *= statusModifier;

        // Casting modifier
        if (enemy.IsCasting)
            baseScore *= 1.5f;

        // Persistence modifier (longer targeting = higher threat)
        var persistenceModifier = Math.Min(2.0f, 1.0f + (history.ConsecutiveFrames / 60.0f));
        baseScore *= persistenceModifier;

        // Line of sight modifier
        if (!HasAdvancedLineOfSight(enemy, localPlayer))
            baseScore *= 0.7f;

        return Math.Max(0.1f, baseScore);
    }

    private static Vector3 GetFacingDirection(IPlayerCharacter player)
    {
        // Convert rotation to facing direction vector
        var rotation = player.Rotation;
        return new Vector3((float)Math.Sin(rotation), 0, (float)Math.Cos(rotation));
    }

    private static void UpdateTargetingHistory(ulong enemyId, DateTime currentTime, Vector3 position, TargetingType targetingType)
    {
        if (_targetingHistory.TryGetValue(enemyId, out var existing))
        {
            _targetingHistory[enemyId] = existing with
            {
                LastTargeted = currentTime,
                ConsecutiveFrames = existing.ConsecutiveFrames + 1,
                LastPosition = position,
                LastTargetingType = targetingType,
                AverageDistance = (existing.AverageDistance + Vector3.Distance(position, existing.LastPosition)) / 2
            };
        }
        else
        {
            _targetingHistory[enemyId] = new TargetingHistory
            {
                FirstTargeted = currentTime,
                LastTargeted = currentTime,
                ConsecutiveFrames = 1,
                LastPosition = position,
                LastTargetingType = targetingType,
                AverageDistance = 0f
            };
        }
    }

    private static void CleanupOldHistory(DateTime currentTime)
    {
        var keysToRemove = _targetingHistory
            .Where(kvp => (currentTime - kvp.Value.LastTargeted) > _historyExpiry)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var key in keysToRemove)
        {
            _targetingHistory.Remove(key);
            _lastKnownPositions.Remove(key);
            _lastMovementUpdate.Remove(key);
        }
    }

    private static Vector3 PredictEnemyPosition(IPlayerCharacter enemy, float timeAhead)
    {
        var currentPos = enemy.Position;
        var enemyId = enemy.GameObjectId;

        if (_lastKnownPositions.TryGetValue(enemyId, out var lastPos) &&
            _lastMovementUpdate.TryGetValue(enemyId, out var lastUpdate))
        {
            var timeDelta = (float)(DateTime.UtcNow - lastUpdate).TotalSeconds;
            if (timeDelta > 0 && timeDelta < 1.0f) // Only predict if we have recent data
            {
                var velocity = (currentPos - lastPos) / timeDelta;
                var predictedPos = currentPos + velocity * timeAhead;

                // Update tracking
                _lastKnownPositions[enemyId] = currentPos;
                _lastMovementUpdate[enemyId] = DateTime.UtcNow;

                return predictedPos;
            }
        }

        // Initialize tracking
        _lastKnownPositions[enemyId] = currentPos;
        _lastMovementUpdate[enemyId] = DateTime.UtcNow;

        return currentPos; // No prediction data available
    }

    private static float CalculateMovementSpeed(IPlayerCharacter enemy)
    {
        var enemyId = enemy.GameObjectId;
        var currentPos = enemy.Position;

        if (_lastKnownPositions.TryGetValue(enemyId, out var lastPos) &&
            _lastMovementUpdate.TryGetValue(enemyId, out var lastUpdate))
        {
            var timeDelta = (float)(DateTime.UtcNow - lastUpdate).TotalSeconds;
            if (timeDelta > 0)
            {
                return Vector3.Distance(currentPos, lastPos) / timeDelta;
            }
        }

        return 0f;
    }

    private static string? GetCastingAbilityName(IPlayerCharacter enemy)
    {
        if (!enemy.IsCasting) return null;

        // Try to get the casting action name
        // This is a simplified version - in a full implementation you'd use action sheets
        return $"Ability {enemy.CastActionId}";
    }

    /// <summary>
    /// Get the remaining cast time for a player's current ability
    /// </summary>
    public static float GetRemainingCastTime(IPlayerCharacter player)
    {
        if (player?.IsValid() != true || !player.IsCasting)
            return 0f;

        return player.TotalCastTime - player.CurrentCastTime;
    }

    private static bool HasAdvancedLineOfSight(IPlayerCharacter from, IPlayerCharacter to)
    {
        if (from?.IsValid() != true || to?.IsValid() != true)
            return false;

        var cacheKey = (from.GameObjectId, to.GameObjectId);
        var now = DateTime.UtcNow;

        // Check cache first
        if (_losCache.TryGetValue(cacheKey, out var cached) &&
            (now - cached.lastCheck) < _cacheExpiry)
        {
            return cached.hasLOS;
        }

        // Perform enhanced line of sight check
        bool hasLOS = PerformAdvancedLineOfSightCheck(from, to);

        // Update cache
        _losCache[cacheKey] = (hasLOS, now);

        // Clean old cache entries periodically
        if (_losCache.Count > 200)
        {
            CleanOldLOSCacheEntries(now);
        }

        return hasLOS;
    }

    private static bool CanEnemyReachYou(IPlayerCharacter enemy, IPlayerCharacter localPlayer, float distance)
    {
        var role = JobHelper.GetPlayerRole(enemy);
        var maxReachDistance = role switch
        {
            PlayerRole.MeleeDPS => 15.0f,      // Gap closers
            PlayerRole.Tank => 20.0f,          // Charges
            PlayerRole.PhysicalRangedDPS => 8.0f,  // Limited mobility
            PlayerRole.MagicalRangedDPS => 6.0f,   // Very limited mobility
            PlayerRole.Healer => 10.0f,        // Some mobility
            _ => 10.0f
        };

        // Factor in movement speed and sprint
        if (StatusEffectHelper.IsSprinting(enemy))
            maxReachDistance *= 1.5f;

        return distance <= maxReachDistance;
    }

    private static EscapeRecommendation DetermineEscapeRecommendation(float threatScore, TargetingType targetingType,
        float distance, PlayerRole enemyRole)
    {
        // Extreme threat - immediate action required
        if (threatScore >= 12.0f || (targetingType == TargetingType.Direct && distance < 5.0f && enemyRole == PlayerRole.MeleeDPS))
            return EscapeRecommendation.ImmediateRetreat;

        // Critical threat - strong defensive action
        if (threatScore >= 8.0f || (targetingType == TargetingType.Casting && distance < 10.0f))
            return EscapeRecommendation.CallForHelp;

        // High threat - use defensive abilities
        if (threatScore >= 5.0f || (targetingType == TargetingType.Direct && distance < MELEE_RANGE))
            return EscapeRecommendation.Escape;

        // Moderate threat - prepare defenses
        if (threatScore >= 3.0f || targetingType == TargetingType.Direct)
            return EscapeRecommendation.DefensiveCooldown;

        // Low threat - reposition if needed
        if (threatScore >= 1.5f)
            return EscapeRecommendation.Reposition;

        return EscapeRecommendation.None;
    }

    private static bool PerformAdvancedLineOfSightCheck(IPlayerCharacter from, IPlayerCharacter to)
    {
        try
        {
            // Get positions with eye level offset
            var fromPos = from.Position + new Vector3(0, 1.5f, 0);
            var toPos = to.Position + new Vector3(0, 1.5f, 0);

            var distance = Vector3.Distance(fromPos, toPos);
            if (distance > 50.0f) return false;

            // Enhanced LOS calculation with multiple factors

            // Very close = always have LOS
            if (distance < 3.0f) return true;

            // Check height difference (major obstacle indicator)
            var heightDiff = Math.Abs(fromPos.Y - toPos.Y);
            if (heightDiff > 4.0f) return false;

            // Check for steep terrain (potential obstacles)
            var horizontalDistance = Vector2.Distance(new Vector2(fromPos.X, fromPos.Z), new Vector2(toPos.X, toPos.Z));
            if (horizontalDistance > 0 && (heightDiff / horizontalDistance) > 0.5f) // Steep slope
                return false;

            // Sample points along the line for more accurate detection
            var direction = Vector3.Normalize(toPos - fromPos);
            var sampleCount = Math.Min(10, (int)(distance / 2.0f));

            for (int i = 1; i < sampleCount; i++)
            {
                var samplePos = fromPos + direction * (distance * i / sampleCount);
                var groundHeight = GetEstimatedGroundHeight(samplePos);

                // If sample point is significantly below estimated ground, likely blocked
                if (samplePos.Y < groundHeight - 2.0f)
                    return false;
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    private static float GetEstimatedGroundHeight(Vector3 position)
    {
        // Simple ground height estimation
        // In a full implementation, you'd use terrain data
        return position.Y; // Placeholder
    }

    private static void CleanOldLOSCacheEntries(DateTime now)
    {
        var keysToRemove = _losCache
            .Where(kvp => (now - kvp.Value.lastCheck) > _cacheExpiry)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var key in keysToRemove)
        {
            _losCache.Remove(key);
        }
    }

    /// <summary>
    /// Get advanced tactical recommendations based on comprehensive analysis
    /// </summary>
    public static List<string> GetAdvancedTacticalRecommendations(List<AdvancedTargetingInfo> targetingAnalysis, TargetingThreatLevel threatLevel)
    {
        var recommendations = new List<string>();

        switch (threatLevel)
        {
            case TargetingThreatLevel.Extreme:
                recommendations.Add("🔥 EXTREME THREAT - IMMEDIATE ACTION REQUIRED!");
                recommendations.Add("💨 Use ALL escape abilities NOW");
                recommendations.Add("🛡️ Pop ALL defensive cooldowns");
                recommendations.Add("📢 EMERGENCY - Call for immediate help");
                break;

            case TargetingThreatLevel.Critical:
                recommendations.Add("🚨 CRITICAL THREAT - Multiple enemies focusing you!");
                recommendations.Add("💨 Use escape abilities immediately");
                recommendations.Add("🛡️ Activate major defensive cooldowns");
                recommendations.Add("📢 Call for ally assistance urgently");
                break;

            case TargetingThreatLevel.High:
                recommendations.Add("⚠️ HIGH THREAT - Dangerous situation");
                recommendations.Add("🏃 Reposition to safer location");
                recommendations.Add("🛡️ Prepare defensive abilities");
                break;

            case TargetingThreatLevel.Moderate:
                recommendations.Add("⚡ MODERATE THREAT - Stay alert");
                recommendations.Add("👀 Watch for incoming abilities");
                recommendations.Add("🎯 Plan escape routes");
                break;

            case TargetingThreatLevel.Low:
                recommendations.Add("👁️ Low threat detected");
                recommendations.Add("✅ Situation manageable");
                break;
        }

        // Add specific tactical advice
        var directTargeters = targetingAnalysis.Where(t => t.TargetingType == TargetingType.Direct).ToList();
        var casters = targetingAnalysis.Where(t => t.IsCasting).ToList();
        var closeEnemies = targetingAnalysis.Where(t => t.Distance < MELEE_RANGE).ToList();

        if (casters.Any())
        {
            var shortestCast = casters.Min(c => c.CastTimeRemaining);
            if (shortestCast < 2.0f)
                recommendations.Add($"⏰ Incoming ability in {shortestCast:F1}s - DODGE NOW!");
        }

        if (closeEnemies.Any())
        {
            recommendations.Add($"⚔️ {closeEnemies.Count} melee enemies in range!");
        }

        return recommendations.Take(6).ToList();
    }

    /// <summary>
    /// Clear all caches and reset tracking data
    /// </summary>
    public static void ClearAllCaches()
    {
        _losCache.Clear();
        _targetingHistory.Clear();
        _lastKnownPositions.Clear();
        _lastMovementUpdate.Clear();
    }

    #endregion

    #region Legacy Compatibility Methods

    /// <summary>
    /// Legacy method for backward compatibility
    /// </summary>
    public static bool IsCasting(IPlayerCharacter player) => player?.IsCasting == true;

    /// <summary>
    /// Legacy method for backward compatibility
    /// </summary>
    public static List<TargetingInfo> GetEnemiesTargetingMe(List<IPlayerCharacter> enemies, IPlayerCharacter localPlayer)
    {
        var advancedAnalysis = GetAdvancedTargetingAnalysis(enemies, localPlayer);
        return advancedAnalysis.Where(a => a.TargetingType == TargetingType.Direct)
            .Select(a => new TargetingInfo
            {
                Targeter = a.Targeter,
                Distance = a.Distance,
                Role = a.Role,
                IsCasting = a.IsCasting,
                HasLineOfSight = a.HasLineOfSight,
                ThreatLevel = a.StatusThreatLevel
            }).ToList();
    }

    /// <summary>
    /// Legacy method for backward compatibility
    /// </summary>
    public static TargetingThreatLevel GetTargetingThreatLevel(List<TargetingInfo> targetingEnemies)
    {
        var count = targetingEnemies.Count;
        return count switch
        {
            0 => TargetingThreatLevel.None,
            1 => TargetingThreatLevel.Low,
            2 => TargetingThreatLevel.Moderate,
            3 => TargetingThreatLevel.High,
            _ => TargetingThreatLevel.Critical
        };
    }

    #endregion
}
