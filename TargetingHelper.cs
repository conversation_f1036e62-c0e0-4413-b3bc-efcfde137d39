using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using Dalamud.Game.ClientState.Objects.SubKinds;
using FFXIVClientStructs.FFXIV.Common.Math;

namespace PvPLinePlugin;

public enum TargetingThreatLevel
{
    None,           // No one targeting you
    Low,            // 1 enemy targeting you
    Moderate,       // 2 enemies targeting you
    High,           // 3+ enemies targeting you
    Critical        // Multiple enemies + dangerous abilities incoming
}

public struct TargetingInfo
{
    public IPlayerCharacter Targeter { get; init; }
    public float Distance { get; init; }
    public PlayerRole Role { get; init; }
    public bool IsCasting { get; init; }
    public bool HasLineOfSight { get; init; }
    public ThreatLevel ThreatLevel { get; init; }
}

public static class TargetingHelper
{
    // Cache for line of sight calculations to improve performance
    private static readonly Dictionary<(uint, uint), (bool hasLOS, DateTime lastCheck)> _losCache = new();
    private static readonly TimeSpan _losCacheExpiry = TimeSpan.FromMilliseconds(500); // Cache for 500ms

    /// <summary>
    /// Check if an enemy player is targeting the local player
    /// </summary>
    public static bool IsTargetingMe(IPlayerCharacter enemy, IPlayerCharacter localPlayer)
    {
        if (enemy?.IsValid() != true || localPlayer?.IsValid() != true)
            return false;

        return enemy.TargetObjectId == localPlayer.GameObjectId;
    }

    /// <summary>
    /// Get all enemies currently targeting the local player
    /// </summary>
    public static List<TargetingInfo> GetEnemiesTargetingMe(List<IPlayerCharacter> enemies, IPlayerCharacter localPlayer)
    {
        if (localPlayer?.IsValid() != true)
            return new List<TargetingInfo>();

        var targetingEnemies = new List<TargetingInfo>();

        foreach (var enemy in enemies)
        {
            if (enemy?.IsValid() != true) continue;
            if (!IsTargetingMe(enemy, localPlayer)) continue;

            var distance = Vector3.Distance(localPlayer.Position, enemy.Position);
            var role = JobHelper.GetPlayerRole(enemy);
            var isCasting = IsCasting(enemy);
            var hasLOS = HasLineOfSight(enemy, localPlayer);
            var threatLevel = StatusEffectHelper.GetThreatLevel(enemy);

            targetingEnemies.Add(new TargetingInfo
            {
                Targeter = enemy,
                Distance = distance,
                Role = role,
                IsCasting = isCasting,
                HasLineOfSight = hasLOS,
                ThreatLevel = threatLevel
            });
        }

        return targetingEnemies.OrderBy(t => t.Distance).ToList();
    }

    /// <summary>
    /// Calculate overall targeting threat level based on who's targeting you
    /// </summary>
    public static TargetingThreatLevel GetTargetingThreatLevel(List<TargetingInfo> targetingEnemies)
    {
        if (!targetingEnemies.Any())
            return TargetingThreatLevel.None;

        var count = targetingEnemies.Count;
        var hasCasters = targetingEnemies.Any(t => t.IsCasting);
        var hasHighThreat = targetingEnemies.Any(t => t.ThreatLevel == ThreatLevel.HighThreat);
        var hasCloseEnemies = targetingEnemies.Any(t => t.Distance < 10.0f);

        // Critical threat conditions
        if ((count >= 3) || (count >= 2 && hasCasters) || (count >= 2 && hasHighThreat))
            return TargetingThreatLevel.Critical;

        // High threat conditions
        if ((count >= 3) || (count >= 2 && hasCloseEnemies))
            return TargetingThreatLevel.High;

        // Moderate threat
        if (count >= 2)
            return TargetingThreatLevel.Moderate;

        // Low threat
        return TargetingThreatLevel.Low;
    }

    /// <summary>
    /// Check if a player is currently casting an ability
    /// </summary>
    public static bool IsCasting(IPlayerCharacter player)
    {
        if (player?.IsValid() != true)
            return false;

        // Check if player has an active cast bar
        return player.IsCasting;
    }

    /// <summary>
    /// Get the remaining cast time for a player's current ability
    /// </summary>
    public static float GetRemainingCastTime(IPlayerCharacter player)
    {
        if (player?.IsValid() != true || !player.IsCasting)
            return 0f;

        return player.TotalCastTime - player.CurrentCastTime;
    }

    /// <summary>
    /// Basic line of sight check between two players
    /// Uses caching to improve performance
    /// </summary>
    public static bool HasLineOfSight(IPlayerCharacter from, IPlayerCharacter to)
    {
        if (from?.IsValid() != true || to?.IsValid() != true)
            return false;

        var cacheKey = (from.GameObjectId, to.GameObjectId);
        var now = DateTime.UtcNow;

        // Check cache first
        if (_losCache.TryGetValue(cacheKey, out var cached) && 
            (now - cached.lastCheck) < _losCacheExpiry)
        {
            return cached.hasLOS;
        }

        // Perform line of sight check
        bool hasLOS = PerformLineOfSightCheck(from, to);

        // Update cache
        _losCache[cacheKey] = (hasLOS, now);

        // Clean old cache entries periodically
        if (_losCache.Count > 100)
        {
            CleanOldCacheEntries(now);
        }

        return hasLOS;
    }

    /// <summary>
    /// Perform the actual line of sight calculation
    /// </summary>
    private static bool PerformLineOfSightCheck(IPlayerCharacter from, IPlayerCharacter to)
    {
        try
        {
            // Get positions with slight height offset for eye level
            var fromPos = from.Position + new Vector3(0, 1.5f, 0);
            var toPos = to.Position + new Vector3(0, 1.5f, 0);

            // Calculate distance - if too far, assume no LOS for performance
            var distance = Vector3.Distance(fromPos, toPos);
            if (distance > 50.0f) // Max reasonable LOS distance in PvP
                return false;

            // For now, use a simplified LOS check based on distance and basic obstacles
            // In a full implementation, you would use FFXIVClientStructs for proper raycast
            // This is a placeholder that assumes LOS unless there are obvious blockers
            
            // Simple heuristic: if players are very close, assume LOS
            if (distance < 5.0f)
                return true;

            // For medium distances, assume LOS unless there's a significant height difference
            var heightDiff = Math.Abs(fromPos.Y - toPos.Y);
            if (heightDiff > 3.0f) // Significant height difference suggests obstacles
                return false;

            // Default to having LOS for most cases
            // TODO: Implement proper raycast collision detection using FFXIVClientStructs
            return true;
        }
        catch
        {
            // If any error occurs, assume no LOS for safety
            return false;
        }
    }

    /// <summary>
    /// Clean old entries from the LOS cache
    /// </summary>
    private static void CleanOldCacheEntries(DateTime now)
    {
        var keysToRemove = _losCache
            .Where(kvp => (now - kvp.Value.lastCheck) > _losCacheExpiry)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var key in keysToRemove)
        {
            _losCache.Remove(key);
        }
    }

    /// <summary>
    /// Get targeting recommendations based on current threat
    /// </summary>
    public static List<string> GetTargetingRecommendations(List<TargetingInfo> targetingEnemies, TargetingThreatLevel threatLevel)
    {
        var recommendations = new List<string>();

        switch (threatLevel)
        {
            case TargetingThreatLevel.Critical:
                recommendations.Add("🚨 CRITICAL: Multiple enemies targeting you!");
                recommendations.Add("💨 Use escape abilities immediately");
                recommendations.Add("🛡️ Activate defensive cooldowns");
                recommendations.Add("📢 Call for ally assistance");
                break;

            case TargetingThreatLevel.High:
                recommendations.Add("⚠️ HIGH THREAT: 3+ enemies targeting you");
                recommendations.Add("🏃 Consider repositioning");
                recommendations.Add("🛡️ Prepare defensive abilities");
                break;

            case TargetingThreatLevel.Moderate:
                recommendations.Add("⚡ MODERATE: 2 enemies targeting you");
                recommendations.Add("👀 Watch for incoming abilities");
                recommendations.Add("🎯 Look for escape routes");
                break;

            case TargetingThreatLevel.Low:
                recommendations.Add("👁️ 1 enemy has you targeted");
                recommendations.Add("✅ Manageable threat level");
                break;
        }

        // Add specific recommendations based on targeting enemies
        var casters = targetingEnemies.Where(t => t.IsCasting).ToList();
        if (casters.Any())
        {
            recommendations.Add($"🔮 {casters.Count} enemy(ies) casting abilities at you!");
        }

        var closeMelee = targetingEnemies.Where(t => t.Role == PlayerRole.MeleeDPS && t.Distance < 8.0f).ToList();
        if (closeMelee.Any())
        {
            recommendations.Add($"⚔️ {closeMelee.Count} melee DPS in range!");
        }

        return recommendations;
    }

    /// <summary>
    /// Clear the LOS cache (useful for cleanup)
    /// </summary>
    public static void ClearCache()
    {
        _losCache.Clear();
    }
}
